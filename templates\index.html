<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Breast Cancer Prediction System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .creator-info {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 30px;
            text-align: center;
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .creator-info .creator-name {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .creator-info .medical-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            align-items: start;
        }

        .form-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .form-header h2 {
            color: #2d3748;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .form-header p {
            color: #718096;
            font-size: 1rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .form-group {
            position: relative;
        }

        .form-group label {
            display: block;
            color: #2d3748;
            font-weight: 500;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .predict-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .predict-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .predict-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .results-panel {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: sticky;
            top: 20px;
        }

        .results-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .results-header h3 {
            color: #2d3748;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .prediction-result {
            text-align: center;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
            display: none;
        }

        .prediction-result.benign {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .prediction-result.malignant {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
        }

        .prediction-result .result-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .prediction-result .result-text {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .prediction-result .confidence {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .loading {
            text-align: center;
            padding: 40px;
            display: none;
        }

        .loading .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }

        .info-panel {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .info-panel h4 {
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .info-panel ul {
            list-style: none;
        }

        .info-panel li {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-heartbeat"></i> Breast Cancer Prediction System</h1>
            <p class="subtitle">AI-Powered Medical Diagnosis Assistant</p>
        </div>

        <div class="creator-info">
            <div class="creator-name">
                <i class="fas fa-user-md"></i> Created by Tumo Olorato Mogame
            </div>
            <div class="medical-badge">
                <i class="fas fa-stethoscope"></i>
                Medical AI Research & Development
            </div>
        </div>

        <div class="main-content">
            <div class="form-container">
                <div class="form-header">
                    <h2><i class="fas fa-microscope"></i> Tumor Analysis Input</h2>
                    <p>Enter the tumor characteristics for AI-powered diagnosis</p>
                </div>

                <div class="error-message" id="errorMessage"></div>

                <form id="predictionForm">
                    <div class="form-grid" id="formGrid">
                        <!-- Form fields will be dynamically generated -->
                    </div>

                    <button type="submit" class="predict-btn" id="predictBtn">
                        <i class="fas fa-brain"></i>
                        Analyze with AI
                    </button>
                </form>
            </div>

            <div class="results-panel">
                <div class="results-header">
                    <h3><i class="fas fa-chart-line"></i> Diagnosis Results</h3>
                    <p>AI-powered analysis results will appear here</p>
                </div>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Analyzing tumor characteristics...</p>
                </div>

                <div class="prediction-result" id="predictionResult">
                    <div class="result-icon" id="resultIcon"></div>
                    <div class="result-text" id="resultText"></div>
                    <div class="confidence" id="confidenceText"></div>
                </div>

                <div class="info-panel">
                    <h4><i class="fas fa-info-circle"></i> About This System</h4>
                    <ul>
                        <li><i class="fas fa-robot"></i> Uses Support Vector Machine (SVM)</li>
                        <li><i class="fas fa-database"></i> Trained on Wisconsin Breast Cancer Dataset</li>
                        <li><i class="fas fa-shield-alt"></i> For research and educational purposes</li>
                        <li><i class="fas fa-user-md"></i> Always consult medical professionals</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Feature definitions with user-friendly names
        const FEATURES = {
            'radius_mean': 'Average Radius',
            'texture_mean': 'Average Texture',
            'perimeter_mean': 'Average Perimeter',
            'area_mean': 'Average Area',
            'smoothness_mean': 'Average Smoothness',
            'compactness_mean': 'Average Compactness',
            'concavity_mean': 'Average Concavity',
            'concave_points_mean': 'Average Concave Points',
            'symmetry_mean': 'Average Symmetry',
            'fractal_dimension_mean': 'Average Fractal Dimension'
        };

        // Initialize the form
        function initializeForm() {
            const formGrid = document.getElementById('formGrid');
            
            Object.entries(FEATURES).forEach(([key, label]) => {
                const formGroup = document.createElement('div');
                formGroup.className = 'form-group';
                
                formGroup.innerHTML = `
                    <label for="${key}">${label}</label>
                    <input type="number" 
                           id="${key}" 
                           name="${key}" 
                           step="0.0001" 
                           placeholder="Enter ${label.toLowerCase()}"
                           required>
                `;
                
                formGrid.appendChild(formGroup);
            });
        }

        // Handle form submission
        document.getElementById('predictionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {};
            
            // Collect form data
            for (let [key, value] of formData.entries()) {
                data[key] = parseFloat(value);
            }
            
            // Add default values for features not in the simplified form
            const allFeatures = [
                'radius_mean', 'texture_mean', 'perimeter_mean', 'area_mean',
                'smoothness_mean', 'compactness_mean', 'concavity_mean',
                'concave_points_mean', 'symmetry_mean', 'fractal_dimension_mean',
                'radius_se', 'texture_se', 'perimeter_se', 'area_se',
                'smoothness_se', 'compactness_se', 'concavity_se',
                'concave_points_se', 'symmetry_se', 'fractal_dimension_se',
                'radius_worst', 'texture_worst', 'perimeter_worst', 'area_worst',
                'smoothness_worst', 'compactness_worst', 'concavity_worst',
                'concave_points_worst', 'symmetry_worst', 'fractal_dimension_worst'
            ];
            
            // Fill missing features with estimated values based on mean features
            allFeatures.forEach(feature => {
                if (!data[feature]) {
                    if (feature.includes('_se')) {
                        // Standard error features - typically 10-20% of mean
                        const meanFeature = feature.replace('_se', '_mean');
                        data[feature] = data[meanFeature] ? data[meanFeature] * 0.15 : 0.1;
                    } else if (feature.includes('_worst')) {
                        // Worst features - typically 120-150% of mean
                        const meanFeature = feature.replace('_worst', '_mean');
                        data[feature] = data[meanFeature] ? data[meanFeature] * 1.3 : 1.0;
                    }
                }
            });
            
            showLoading();
            hideError();
            
            try {
                const response = await fetch('/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult(result);
                } else {
                    showError(result.error || 'Prediction failed');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            } finally {
                hideLoading();
            }
        });

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('predictionResult').style.display = 'none';
            document.getElementById('predictBtn').disabled = true;
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('predictBtn').disabled = false;
        }

        function showResult(result) {
            const resultDiv = document.getElementById('predictionResult');
            const resultIcon = document.getElementById('resultIcon');
            const resultText = document.getElementById('resultText');
            const confidenceText = document.getElementById('confidenceText');
            
            resultDiv.className = 'prediction-result ' + result.prediction.toLowerCase();
            
            if (result.prediction === 'Benign') {
                resultIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
                resultText.textContent = 'Benign Tumor';
            } else {
                resultIcon.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                resultText.textContent = 'Malignant Tumor';
            }
            
            confidenceText.textContent = `Confidence: ${result.confidence}%`;
            resultDiv.style.display = 'block';
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        // Initialize the form when the page loads
        document.addEventListener('DOMContentLoaded', initializeForm);
    </script>
</body>
</html>
